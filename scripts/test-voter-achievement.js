#!/usr/bin/env node

/**
 * Test script to verify voter achievement progress tracking
 */

import { config } from 'dotenv';
import AchievementService from '../build/services/AchievementService.js';
import db from '../build/utils/Db.js';

// Load environment variables
config();

const TEST_USER_ID = '701727675311587358'; // Test user ID
const achievementService = new AchievementService();

async function main() {
  console.log('🗳️  Testing Voter Achievement Progress...\n');

  try {
    // Clean up any existing test data
    await cleanupTestData();

    // Test voter achievement progress tracking
    await testVoterAchievementProgress();

    // Test super voter achievement
    await testSuperVoterAchievement();

    console.log('\n✅ All voter achievement tests completed!');
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    // Clean up test data
    await cleanupTestData();
    await db.$disconnect();
  }
}

async function cleanupTestData() {
  console.log('🧹 Cleaning up test data...');
  
  // Remove test user achievements
  await db.userAchievement.deleteMany({
    where: { userId: TEST_USER_ID }
  });

  // Remove test user progress
  await db.userAchievementProgress.deleteMany({
    where: { userId: TEST_USER_ID }
  });

  console.log('✅ Test data cleaned up');
}

async function testVoterAchievementProgress() {
  console.log('📊 Testing voter achievement progress tracking...');

  // Simulate votes 1-9 (should not unlock achievement)
  for (let voteCount = 1; voteCount <= 9; voteCount++) {
    await achievementService.processEvent('vote', {
      userId: TEST_USER_ID,
      voteCount: voteCount
    });

    const progress = await achievementService.getProgress(TEST_USER_ID, 'voter');
    const isUnlocked = await achievementService.isAchievementUnlocked(TEST_USER_ID, 'voter');

    console.log(`Vote ${voteCount}: Progress = ${progress}/10, Unlocked = ${isUnlocked}`);

    // Verify progress is correct
    if (progress !== voteCount) {
      throw new Error(`Expected progress ${voteCount}, got ${progress}`);
    }

    // Verify not unlocked yet
    if (isUnlocked) {
      throw new Error(`Achievement should not be unlocked at ${voteCount} votes`);
    }
  }

  // Simulate 10th vote (should unlock achievement)
  await achievementService.processEvent('vote', {
    userId: TEST_USER_ID,
    voteCount: 10
  });

  const finalProgress = await achievementService.getProgress(TEST_USER_ID, 'voter');
  const isUnlocked = await achievementService.isAchievementUnlocked(TEST_USER_ID, 'voter');

  console.log(`Vote 10: Progress = ${finalProgress}/10, Unlocked = ${isUnlocked}`);

  // Verify achievement is unlocked
  if (!isUnlocked) {
    throw new Error('Voter achievement should be unlocked at 10 votes');
  }

  console.log('✅ Voter achievement progress tracking works correctly!');
}

async function testSuperVoterAchievement() {
  console.log('\n📊 Testing super voter achievement...');

  // Simulate 100 votes (should unlock super voter)
  await achievementService.processEvent('vote', {
    userId: TEST_USER_ID,
    voteCount: 100
  });

  const progress = await achievementService.getProgress(TEST_USER_ID, 'super-voter');
  const isUnlocked = await achievementService.isAchievementUnlocked(TEST_USER_ID, 'super-voter');

  console.log(`Vote 100: Progress = ${progress}/100, Unlocked = ${isUnlocked}`);

  // Verify super voter achievement is unlocked
  if (!isUnlocked) {
    throw new Error('Super Voter achievement should be unlocked at 100 votes');
  }

  console.log('✅ Super voter achievement works correctly!');
}

// Run the test
main().catch(console.error);
