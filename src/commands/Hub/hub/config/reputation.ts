/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { hubOption } from '#src/commands/Hub/hub/index.js';
import BaseCommand from '#src/core/BaseCommand.js';
import type ComponentContext from '#src/core/CommandContext/ComponentContext.js';
import type Context from '#src/core/CommandContext/Context.js';
import { RegisterInteractionHandler } from '#src/decorators/RegisterInteractionHandler.js';
import { HubReputationService } from '#src/services/HubReputationService.js';
import { HubService } from '#src/services/HubService.js';
import { CustomID } from '#src/utils/CustomID.js';
import { getEmoji } from '#src/utils/EmojiUtils.js';
import { runHubRoleChecksAndReply } from '#src/utils/hub/utils.js';
import { stripIndents } from 'common-tags';
import {
  ButtonBuilder,
  ButtonStyle,
  ContainerBuilder,
  SectionBuilder,
  SeparatorSpacingSize,
  StringSelectMenuBuilder,
  TextDisplayBuilder,
} from 'discord.js';

const CustomIdPrefix = 'hub_config_reputation';

export default class HubConfigReputationSubcommand extends BaseCommand {
  private readonly hubService = new HubService();
  private readonly reputationService = new HubReputationService();

  constructor() {
    super({
      name: 'reputation',
      description: '🏆 Configure reputation system settings for your hub.',
      types: { slash: true, prefix: true },
      options: [hubOption],
    });
  }

  async execute(ctx: Context) {
    const hubName = ctx.options.getString('hub');
    const hub = hubName ? (await this.hubService.findHubsByName(hubName)).at(0) : null;

    if (
      !hub ||
      !(await runHubRoleChecksAndReply(hub, ctx, {
        checkIfManager: true,
      }))
    ) return;

    const container = await this.getReputationSettingsMenu(hub.id);

    await ctx.reply({
      components: [container],
      flags: ['IsComponentsV2'],
    });
  }

  @RegisterInteractionHandler(CustomIdPrefix, 'edit_setting')
  async handleEditSetting(ctx: ComponentContext) {
    const [hubId, setting] = ctx.customId.args as [string, string];

    const hub = await this.hubService.fetchHub(hubId);
    if (!hub) return;

    if (!(await runHubRoleChecksAndReply(hub, ctx, { checkIfManager: true }))) {
      return;
    }

    const container = this.getEditSettingMenu(hubId, setting);

    await ctx.editReply({
      components: [container],
      flags: ['IsComponentsV2'],
    });
  }

  @RegisterInteractionHandler(CustomIdPrefix, 'set_value')
  async handleSetValue(ctx: ComponentContext) {
    // Get the value from the select menu interaction
    const selectedValue = ctx.isStringSelectMenu() ? ctx.values?.[0] : ctx.customId.args[2];
    const [hubId, setting] = ctx.customId.args;

    const hub = await this.hubService.fetchHub(hubId);
    if (!hub) return;

    if (!(await runHubRoleChecksAndReply(hub, ctx, { checkIfManager: true }))) {
      return;
    }

    if (!selectedValue) {
      await ctx.reply({
        content: `${getEmoji('x_icon', ctx.client)} No value selected.`,
        flags: ['Ephemeral'],
      });
      return;
    }

    const numericValue = parseFloat(selectedValue);
    if (isNaN(numericValue) || numericValue < 0) {
      await ctx.reply({
        content: `${getEmoji('x_icon', ctx.client)} Invalid value. Please enter a positive number.`,
        flags: ['Ephemeral'],
      });
      return;
    }

    // Update the setting
    const updateData: Record<string, number> = {};
    updateData[setting] = numericValue;

    await this.reputationService.updateHubReputationSettings(hubId, updateData);

    const container = await this.getReputationSettingsMenu(hubId);

    await ctx.editReply({
      components: [container],
      flags: ['IsComponentsV2'],
    });

    await ctx.reply({
      content: `${getEmoji('tick_icon', ctx.client)} Successfully updated **${this.getSettingDisplayName(setting)}** to **${numericValue} RP**.`,
      flags: ['Ephemeral'],
    });
  }

  @RegisterInteractionHandler(CustomIdPrefix, 'back')
  async handleBack(ctx: ComponentContext) {
    const [hubId] = ctx.customId.args as [string];

    const container = await this.getReputationSettingsMenu(hubId);

    await ctx.editReply({
      components: [container],
      flags: ['IsComponentsV2'],
    });
  }

  @RegisterInteractionHandler(CustomIdPrefix, 'reset_defaults')
  async handleResetDefaults(ctx: ComponentContext) {
    const [hubId] = ctx.customId.args as [string];

    const hub = await this.hubService.fetchHub(hubId);
    if (!hub) return;

    if (!(await runHubRoleChecksAndReply(hub, ctx, { checkIfManager: true }))) {
      return;
    }

    // Reset to default values
    await this.reputationService.updateHubReputationSettings(hubId, {
      messageRewardMin: 0.1,
      messageRewardMax: 0.5,
      imageUploadRequirement: 2.0,
      gifUploadRequirement: 5.0,
      videoUploadRequirement: 10.0,
      stickerRequirement: 3.0,
      longMessageRequirement: 1.0,
      embedRequirement: 4.0,
    });

    const container = await this.getReputationSettingsMenu(hubId);

    await ctx.editReply({
      components: [container],
      flags: ['IsComponentsV2'],
    });

    await ctx.reply({
      content: `${getEmoji('tick_icon', ctx.client)} Successfully reset all reputation settings to default values.`,
      flags: ['Ephemeral'],
    });
  }

  private async getReputationSettingsMenu(hubId: string) {
    const settings = await this.reputationService.getHubReputationSettings(hubId);
    const container = new ContainerBuilder();

    // Header
    const headerText = new TextDisplayBuilder().setContent(
      stripIndents`
      ## 🏆 Reputation System Settings
      Configure reputation requirements for various features in your hub.
      
      **How it works:**
      • Users earn ${settings.messageRewardMin}-${settings.messageRewardMax} RP per message
      • Features require minimum RP to unlock
      • Violations result in RP penalties
      `,
    );
    container.addTextDisplayComponents(headerText);

    // Separator
    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    // Message Rewards Section
    const rewardsSection = new SectionBuilder()
      .addTextDisplayComponents(
        new TextDisplayBuilder().setContent(
          `### 💰 Message Rewards\n**Min RP per message:** ${settings.messageRewardMin}\n**Max RP per message:** ${settings.messageRewardMax}`,
        ),
      )
      .setButtonAccessory(
        new ButtonBuilder()
          .setCustomId(
            new CustomID()
              .setIdentifier(CustomIdPrefix, 'edit_setting')
              .setArgs(hubId, 'messageRewardMin')
              .toString(),
          )
          .setStyle(ButtonStyle.Secondary)
          .setLabel('Edit'),
      );
    container.addSectionComponents(rewardsSection);

    // Feature Requirements Section
    const requirementsData = [
      { key: 'imageUploadRequirement', name: '🖼️ Image Uploads', value: settings.imageUploadRequirement },
      { key: 'gifUploadRequirement', name: '🎞️ GIF Uploads', value: settings.gifUploadRequirement },
      { key: 'videoUploadRequirement', name: '🎥 Video Uploads', value: settings.videoUploadRequirement },
      { key: 'stickerRequirement', name: '😀 Stickers', value: settings.stickerRequirement },
      { key: 'longMessageRequirement', name: '📝 Long Messages', value: settings.longMessageRequirement },
      { key: 'embedRequirement', name: '🔗 Rich Content (links)', value: settings.embedRequirement },
    ];

    requirementsData.forEach((req) => {
      const section = new SectionBuilder()
        .addTextDisplayComponents(
          new TextDisplayBuilder().setContent(`### ${req.name}\n**Required RP:** ${req.value}`),
        )
        .setButtonAccessory(
          new ButtonBuilder()
            .setCustomId(
              new CustomID()
                .setIdentifier(CustomIdPrefix, 'edit_setting')
                .setArgs(hubId, req.key)
                .toString(),
            )
            .setStyle(ButtonStyle.Secondary)
            .setLabel('Edit'),
        );
      container.addSectionComponents(section);
    });

    // Separator
    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    // Reset button
    const resetSection = new SectionBuilder()
      .addTextDisplayComponents(
        new TextDisplayBuilder().setContent('### ⚠️ Reset to Defaults\nRestore all settings to their default values.'),
      )
      .setButtonAccessory(
        new ButtonBuilder()
          .setCustomId(
            new CustomID()
              .setIdentifier(CustomIdPrefix, 'reset_defaults')
              .setArgs(hubId)
              .toString(),
          )
          .setStyle(ButtonStyle.Danger)
          .setLabel('Reset All'),
      );
    container.addSectionComponents(resetSection);

    return container;
  }

  private getEditSettingMenu(hubId: string, setting: string) {
    const container = new ContainerBuilder();

    // Header
    const headerText = new TextDisplayBuilder().setContent(
      `## ✏️ Edit ${this.getSettingDisplayName(setting)}\nSelect a new value for this setting.`,
    );
    container.addTextDisplayComponents(headerText);

    // Separator
    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    // Value selection
    const values = this.getSettingValues(setting);
    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId(
        new CustomID()
          .setIdentifier(CustomIdPrefix, 'set_value')
          .setArgs(hubId, setting)
          .toString(),
      )
      .setPlaceholder('Select a value...')
      .setOptions(
        values.map((value) => ({
          label: `${value} RP`,
          value: value.toString(),
          description: this.getValueDescription(setting, value),
        })),
      );

    // Add select menu description as text display
    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent('### Select Value\nChoose the reputation requirement for this feature.'),
    );

    // Add select menu as action row
    container.addActionRowComponents((row) => row.addComponents(selectMenu));

    // Back button section
    const backSection = new SectionBuilder()
      .addTextDisplayComponents(
        new TextDisplayBuilder().setContent('### ← Go Back\nReturn to the main reputation settings menu.'),
      )
      .setButtonAccessory(
        new ButtonBuilder()
          .setCustomId(
            new CustomID()
              .setIdentifier(CustomIdPrefix, 'back')
              .setArgs(hubId)
              .toString(),
          )
          .setStyle(ButtonStyle.Secondary)
          .setLabel('← Back'),
      );
    container.addSectionComponents(backSection);

    return container;
  }

  private getSettingDisplayName(setting: string): string {
    const names: Record<string, string> = {
      messageRewardMin: 'Minimum Message Reward',
      messageRewardMax: 'Maximum Message Reward',
      imageUploadRequirement: 'Image Upload Requirement',
      gifUploadRequirement: 'GIF Upload Requirement',
      videoUploadRequirement: 'Video Upload Requirement',
      stickerRequirement: 'Sticker Requirement',
      longMessageRequirement: 'Long Message Requirement',
      embedRequirement: 'Links & Formatting Requirement',
    };
    return names[setting] || setting;
  }

  private getSettingValues(setting: string): number[] {
    if (setting.includes('messageReward')) {
      return [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0];
    }
    return [0, 1, 2, 3, 4, 5, 7, 10, 15, 20, 25, 30];
  }

  private getValueDescription(setting: string, value: number): string {
    if (value === 0) return 'No requirement (always allowed)';
    if (setting.includes('messageReward')) {
      return `${value} RP awarded per message`;
    }
    if (value <= 2) return 'Very easy to achieve';
    if (value <= 5) return 'Easy to achieve';
    if (value <= 10) return 'Moderate requirement';
    if (value <= 20) return 'High requirement';
    return 'Very high requirement';
  }
}
