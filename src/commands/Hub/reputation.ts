/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import BaseCommand from '#src/core/BaseCommand.js';
import { HubReputationService } from '#src/services/HubReputationService.js';
import { getEmoji } from '#src/utils/EmojiUtils.js';
import { RegisterInteractionHandler } from '#src/decorators/RegisterInteractionHandler.js';
import { CustomID } from '#src/utils/CustomID.js';
import type Context from '#src/core/CommandContext/Context.js';
import type ComponentContext from '#src/core/CommandContext/ComponentContext.js';
import type { HubReputation } from '#src/generated/prisma/client/client.js';
import {
  ContainerBuilder,
  TextDisplayBuilder,
  SectionBuilder,
  ButtonBuilder,
  SeparatorSpacingSize,
  ButtonStyle,
  ApplicationCommandOptionType,
  type User,
} from 'discord.js';
import { stripIndents } from 'common-tags';

const CustomIdPrefix = 'reputation';

export default class ReputationCommand extends BaseCommand {
  private readonly reputationService = new HubReputationService();

  constructor() {
    super({
      name: 'reputation',
      description: '🏆 View your reputation across hubs or check another user\'s reputation.',
      types: { slash: true, prefix: true },
      options: [
        {
          name: 'user',
          description: 'User to check reputation for (defaults to yourself)',
          type: ApplicationCommandOptionType.User,
          required: false,
        },
      ],
    });
  }

  async execute(ctx: Context) {
    const targetUser = (await ctx.options.getUser('user')) ?? ctx.user;
    const isOwnReputation = targetUser.id === ctx.user.id;

    const reputationData = await this.reputationService.getUserReputationAcrossHubs(targetUser.id);

    if (reputationData.length === 0) {
      await ctx.reply({
        content: `${getEmoji('info_icon', ctx.client)} ${isOwnReputation ? 'You don\'t' : `${targetUser.username} doesn't`} have any reputation in any hubs yet. ${isOwnReputation ? 'Start' : 'They can start'} participating in hub conversations to earn reputation!`,
        flags: ['Ephemeral'],
      });
      return;
    }

    const container = this.buildReputationDisplay(targetUser, reputationData, isOwnReputation);

    await ctx.reply({
      components: [container],
      flags: ['IsComponentsV2'],
    });
  }

  @RegisterInteractionHandler(CustomIdPrefix, 'refresh')
  async handleRefresh(ctx: ComponentContext) {
    const [userId] = ctx.customId.args as [string];
    const targetUser = await ctx.client.users.fetch(userId).catch(() => null);

    if (!targetUser) {
      await ctx.reply({
        content: `${getEmoji('x_icon', ctx.client)} Unable to fetch user data.`,
        flags: ['Ephemeral'],
      });
      return;
    }

    const isOwnReputation = targetUser.id === ctx.user.id;
    const reputationData = await this.reputationService.getUserReputationAcrossHubs(targetUser.id);

    if (reputationData.length === 0) {
      await ctx.editReply({
        content: `${getEmoji('info_icon', ctx.client)} ${isOwnReputation ? 'You don\'t' : `${targetUser.username} doesn't`} have any reputation in any hubs yet.`,
        components: [],
      });
      return;
    }

    const container = this.buildReputationDisplay(targetUser, reputationData, isOwnReputation);

    await ctx.editReply({
      components: [container],
    });
  }

  private buildReputationDisplay(
    user: User,
    reputationData: (HubReputation & {
      hub: { id: string; name: string; iconUrl: string | null };
    })[],
    isOwnReputation: boolean,
  ) {
    const container = new ContainerBuilder();

    // Calculate total reputation
    const totalReputation = reputationData.reduce((sum, data) => sum + data.reputation, 0);

    // Header
    const headerText = new TextDisplayBuilder().setContent(
      stripIndents`
      ## 🏆 ${isOwnReputation ? 'Your' : `${user.username}'s`} Reputation
      
      **Total Reputation:** ${totalReputation.toFixed(1)} RP across ${reputationData.length} hub${reputationData.length === 1 ? '' : 's'}
      
      ${isOwnReputation ? 'Your reputation unlocks features and shows your standing in each hub.' : `This shows ${user.username}'s reputation across all hubs they participate in.`}
      `,
    );
    container.addTextDisplayComponents(headerText);

    // Separator
    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    // Sort by reputation (highest first)
    const sortedData = reputationData.sort((a, b) => b.reputation - a.reputation);

    // Add each hub's reputation as text displays (not sections)
    sortedData.forEach((data, index) => {
      const rank = index + 1;
      const rankEmoji = rank === 1 ? '🥇' : rank === 2 ? '🥈' : rank === 3 ? '🥉' : `#${rank}`;

      const textDisplay = new TextDisplayBuilder().setContent(
        stripIndents`
        ### ${rankEmoji} ${data.hub.name}
        **Reputation:** ${data.reputation.toFixed(1)} RP
        ${this.getReputationLevel(data.reputation)}
        `,
      );

      container.addTextDisplayComponents(textDisplay);
    });

    // Separator
    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    // Refresh button
    const refreshSection = new SectionBuilder()
      .addTextDisplayComponents(
        new TextDisplayBuilder().setContent(
          '### 🔄 Refresh Data\nGet the latest reputation information.',
        ),
      )
      .setButtonAccessory(
        new ButtonBuilder()
          .setCustomId(
            new CustomID().setIdentifier(CustomIdPrefix, 'refresh').setArgs(user.id).toString(),
          )
          .setStyle(ButtonStyle.Secondary)
          .setLabel('Refresh'),
      );
    container.addSectionComponents(refreshSection);

    // Tips section for own reputation
    if (isOwnReputation) {
      const tipsText = new TextDisplayBuilder().setContent(
        stripIndents`
        ### 💡 Tips to Earn Reputation
        - Send messages in hubs to earn 0.1-0.5 RP per message
        - Longer messages earn slightly more RP
        - Avoid violations that result in RP penalties
        - Different hubs may have different RP requirements for features
        `,
      );
      container.addTextDisplayComponents(tipsText);
    }

    return container;
  }

  private getReputationLevel(reputation: number): string {
    if (reputation >= 100) return '🌟 **Legendary** - Maximum respect and privileges';
    if (reputation >= 50) return '💎 **Expert** - High standing with full access';
    if (reputation >= 25) return '🔥 **Veteran** - Well-established member';
    if (reputation >= 10) return '⭐ **Regular** - Trusted community member';
    if (reputation >= 5) return '🌱 **Active** - Growing reputation';
    if (reputation >= 1) return '🆕 **Newcomer** - Building reputation';
    return '👤 **Starting** - Just getting started';
  }
}
