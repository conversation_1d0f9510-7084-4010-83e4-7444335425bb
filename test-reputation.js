/*
 * Simple test script to verify the reputation system works
 * Run with: node test-reputation.js
 */

const { HubReputationService } = require('./build/services/HubReputationService.js');

async function testReputationSystem() {
  console.log('🧪 Testing Reputation System...\n');
  
  const reputationService = new HubReputationService();
  const testUserId = '123456789012345678';
  const testHubId = 'test-hub-id';
  
  try {
    // Test 1: Get initial reputation (should be 0)
    console.log('📊 Test 1: Getting initial reputation...');
    const initialRP = await reputationService.getUserReputation(testUserId, testHubId);
    console.log(`Initial RP: ${initialRP} (expected: 0)`);
    
    // Test 2: Award message reputation
    console.log('\n💰 Test 2: Awarding message reputation...');
    const newRP = await reputationService.awardMessageReputation(testUserId, testHubId, 50);
    console.log(`New RP after message: ${newRP} (expected: 0.1-0.5)`);
    
    // Test 3: Check reputation requirements
    console.log('\n🔒 Test 3: Checking reputation requirements...');
    const requirements = await reputationService.getHubReputationRequirements(testHubId);
    console.log('Default requirements:', requirements);
    
    // Test 4: Check if user meets image upload requirement
    console.log('\n🖼️ Test 4: Checking image upload requirement...');
    const canUploadImages = await reputationService.checkReputationRequirement(
      testUserId, 
      testHubId, 
      'imageUpload'
    );
    console.log(`Can upload images: ${canUploadImages} (expected: false initially)`);
    
    // Test 5: Award more reputation to meet requirements
    console.log('\n⬆️ Test 5: Awarding more reputation...');
    for (let i = 0; i < 20; i++) {
      await reputationService.awardMessageReputation(testUserId, testHubId, 100);
    }
    
    const finalRP = await reputationService.getUserReputation(testUserId, testHubId);
    console.log(`Final RP: ${finalRP} (expected: ~2-10)`);
    
    // Test 6: Check if user can now upload images
    console.log('\n✅ Test 6: Checking image upload requirement again...');
    const canUploadImagesNow = await reputationService.checkReputationRequirement(
      testUserId, 
      testHubId, 
      'imageUpload'
    );
    console.log(`Can upload images now: ${canUploadImagesNow} (expected: true if RP >= 2)`);
    
    // Test 7: Apply penalty
    console.log('\n⚠️ Test 7: Applying penalty...');
    const rpAfterPenalty = await reputationService.applyPenalty(
      testUserId, 
      testHubId, 
      'WARNING'
    );
    console.log(`RP after warning penalty: ${rpAfterPenalty} (expected: ${finalRP - 2})`);
    
    console.log('\n🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  testReputationSystem().then(() => {
    console.log('\n✨ Test script finished');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Test script failed:', error);
    process.exit(1);
  });
}

module.exports = { testReputationSystem };
